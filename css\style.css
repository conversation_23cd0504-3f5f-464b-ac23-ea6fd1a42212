:root {
  --bg: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  --glass: rgba(255, 255, 255, 0.1);
  --text: #ffffff;
  --muted: #94a3b8;
  --accent: #e2e8f0;
  --primary: #06b6d4;
  --primary-light: #22d3ee;

  /* Gradient Effects */
  --gradient: linear-gradient(135deg, #06b6d4 0%, #14b8a6 100%);
  --gradient-hover: linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%);

  /* Animation & Transition Settings */
  --transition: all 0.3s ease;

  /* Shadow Effects */
  --shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.25);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* HTML Base Settings */
html {
  scroll-behavior: smooth; /* Smooth scrolling for anchor links */
}

/* Body Base Styling */
body {
  font-family: 'Segoe UI', -apple-system, sans-serif; /* Modern system fonts */
  background: var(--bg); /* Gradient background */
  color: var(--text); /* Primary text color */
  line-height: 1.6; /* Readable line spacing */
  min-height: 100vh; /* Full viewport height minimum */
}

/* Main Container for Content Width Control */
.container {
  max-width: 1200px; /* Maximum content width */
  margin: 0 auto; /* Center horizontally */
  padding: 0 2rem; /* Side padding for mobile */
}

.bg-shapes {
  position: fixed; /* Fixed positioning to stay in place during scroll */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none; /* Don't interfere with user interactions */
  z-index: -1; /* Behind all content */
  overflow: hidden; /* Hide shapes that go outside viewport */
}

/* Base Shape Styling */
.shape {
  position: absolute;
  border-radius: 50%; /* Perfect circles */
  animation: float 12s ease-in-out infinite; /* Floating animation */
  filter: blur(1px); /* Subtle blur for depth */
  transition: var(--transition);
}

/* Individual Shape Positions and Colors */
.shape-1 {
  width: 400px;
  height: 400px;
  top: 5%;
  right: 5%;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.2), rgba(20, 184, 166, 0.1) 50%, transparent 80%);
  animation-delay: 0s; /* Start immediately */
}

.shape-2 {
  width: 300px;
  height: 300px;
  bottom: 15%;
  left: 0;
  animation-delay: 4s; /* Staggered animation start */
  background: radial-gradient(circle, rgba(34, 211, 238, 0.18), rgba(6, 182, 212, 0.08) 50%, transparent 80%);
}

.shape-3 {
  width: 250px;
  height: 250px;
  top: 50%;
  right: 25%;
  animation-delay: 8s; /* Further staggered start */
  background: radial-gradient(circle, rgba(20, 184, 166, 0.15), rgba(34, 211, 238, 0.05) 50%, transparent 80%);
}

.navbar {
  background: var(--glass); /* Semi-transparent background */
  backdrop-filter: blur(20px); /* Glassmorphism blur effect */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
  position: sticky; /* Stick to top when scrolling */
  top: 0;
  z-index: 1000; /* Above all other content */
  padding: 1rem 0;
  transition: var(--transition);
  animation: slideDown 0.8s ease-out; /* Entry animation */
}

/* Navbar Hover Effects */
.navbar:hover {
  background: rgba(6, 182, 212, 0.08); /* Slight color tint on hover */
  border-bottom-color: rgba(6, 182, 212, 0.2);
  box-shadow: 0 4px 20px rgba(6, 182, 212, 0.1), 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Navigation Content Layout */
.nav-content {
  display: flex;
  justify-content: space-between; /* Logo left, menu right */
  align-items: center;
}

/* Logo Styling */
.logo {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text);
  text-decoration: none;
  position: relative;
  transition: var(--transition);
}

/* Logo Underline Effect */
.logo::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0; /* Start with no width */
  height: 3px;
  background: linear-gradient(90deg, #06b6d4, #14b8a6, #22d3ee); /* Gradient underline */
  transition: var(--transition);
  border-radius: 2px;
}

/* Logo Hover Effects */
.logo:hover {
  color: #06b6d4; /* Change text color */
  transform: scale(1.03); /* Slight scale up */
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.4), 0 0 40px rgba(6, 182, 212, 0.2); /* Glow effect */
}

/* Logo Underline Animation on Hover */
.logo:hover::after {
  width: 100%; /* Expand underline to full width */
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.5); /* Add glow to underline */
}

/* Navigation Menu Container */
.nav-menu {
  list-style: none; /* Remove default list bullets */
  display: flex;
  gap: 0.5rem; /* Space between menu items */
}

/* Navigation Menu Items with Staggered Animation */
.nav-menu li {
  animation: fadeInUp 0.8s ease-out;
  animation-fill-mode: both; /* Maintain animation end state */
}

/* Staggered Animation Delays for Menu Items */
.nav-menu li:nth-child(1) { animation-delay: 0.1s; } /* First item */
.nav-menu li:nth-child(2) { animation-delay: 0.2s; } /* Second item */
.nav-menu li:nth-child(3) { animation-delay: 0.3s; } /* Third item */
.nav-menu li:nth-child(4) { animation-delay: 0.4s; } /* Fourth item */

.nav-link {
  color: var(--muted);
  text-decoration: none;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  transition: var(--transition);
  font-weight: 500;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.15) 0%, rgba(20, 184, 166, 0.1) 100%);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
  border-radius: 12px;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #06b6d4, #14b8a6);
  transition: var(--transition);
  transform: translateX(-50%);
  border-radius: 1px;
}

.nav-link:hover,
.nav-link.active {
  color: #ffffff;
  transform: translateY(-2px);
  border-color: rgba(6, 182, 212, 0.3);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.15), 0 4px 12px rgba(20, 184, 166, 0.1);
}

.nav-link:hover::before,
.nav-link.active::before {
  opacity: 1;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

.hero {
  padding: 6rem 0; /* Generous vertical padding */
  min-height: 90vh; /* Nearly full viewport height */
  display: flex;
  align-items: center; /* Vertically center content */
}

.hero .container {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.hero-content {
  flex: 1;
  animation: slideInLeft 0.8s ease-out;
}

.main-title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #fff 0%, #22d3ee 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: fadeInUp 0.8s ease-out 0.2s both;
  position: relative;
}

.main-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient);
  transform: scaleX(0);
  transform-origin: left;
  animation: expandWidth 1s ease-out 1s both;
}

.subtitle {
  font-size: 1.5rem;
  color: var(--primary);
  margin-bottom: 1.5rem;
  font-weight: 600;
  animation: fadeInUp 0.8s ease-out 0.4s both;
  transition: var(--transition);
}

.subtitle:hover {
  color: var(--primary-light);
  transform: translateX(10px);
}

.hero-description {
  font-size: 1.1rem;
  color: var(--accent);
  margin-bottom: 2rem;
  line-height: 1.7;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.stat {
  text-align: center;
  transition: var(--transition);
  padding: 1rem;
  border-radius: 8px;
}

.stat:hover {
  background: rgba(6, 182, 212, 0.1);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 0 30px rgba(6, 182, 212, 0.3);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  transition: var(--transition);
}

.stat:hover .stat-number {
  color: var(--primary-light);
  transform: scale(1.1);
}

.stat-text {
  font-size: 0.9rem;
  color: var(--muted);
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: var(--transition);
}

.stat:hover .stat-text {
  color: var(--text);
}

/* Button */
.cta-btn {
  display: inline-block;
  background: var(--gradient);
  color: var(--text);
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 1s both;
  border: 2px solid transparent;
}

.cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-hover);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.cta-btn::after {
  content: '→';
  margin-left: 0.5rem;
  transition: var(--transition);
  display: inline-block;
}

.cta-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: var(--shadow-hover);
  border-color: var(--primary-light);
}

.cta-btn:hover::before {
  opacity: 1;
}

.cta-btn:hover::after {
  transform: translateX(8px) rotate(45deg);
}

.cta-btn:active {
  transform: translateY(-2px) scale(1.02);
}

/* Hero Image */
.hero-image {
  flex: 1;
  text-align: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: slideInRight 0.8s ease-out 0.3s both;
}

.hero-image::before {
  content: '';
  position: absolute;
  width: 500px;
  height: 500px;
  background: radial-gradient(
    ellipse at center,
    rgba(6, 182, 212, 0.4) 0%,
    rgba(20, 184, 166, 0.3) 30%,
    rgba(34, 211, 238, 0.25) 60%,
    rgba(6, 182, 212, 0.1) 80%,
    transparent 100%
  );
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: morphBlob 12s ease-in-out infinite;
  z-index: -1;
  filter: blur(2px);
  transition: var(--transition);
}

.hero-image:hover::before {
  filter: blur(1px);
  transform: scale(1.1);
}

.hero-image img {
  max-width: 350px;
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(6, 182, 212, 0.4);
  transition: var(--transition);
  position: relative;
  z-index: 1;
  border: 3px solid rgba(255, 255, 255, 0.1);
  animation: fadeInScale 1s ease-out 0.5s both;
}

.hero-image img:hover {
  transform: scale(1.08) translateY(-8px) rotateY(5deg);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5), 0 0 50px rgba(6, 182, 212, 0.8), inset 0 0 20px rgba(6, 182, 212, 0.2);
  border-color: var(--primary);
}

/* Sections */
section {
  padding: 5rem 0;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out both;
}

section:nth-of-type(1) { animation-delay: 0.2s; }
section:nth-of-type(2) { animation-delay: 0.4s; }
section:nth-of-type(3) { animation-delay: 0.6s; }
section:nth-of-type(4) { animation-delay: 0.8s; }

section h2 {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  transition: var(--transition);
}

section h2:hover {
  color: var(--primary);
  transform: translateY(-5px);
}

section h2::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 60px;
  height: 3px;
  background: var(--gradient);
  transition: var(--transition);
  animation: expandWidth 1s ease-out 0.5s both;
}

section h2:hover::after {
  transform: translateX(-50%) scaleX(1.2);
  box-shadow: 0 0 30px rgba(6, 182, 212, 0.3);
}

/* About Section */
.about-section {
  background: rgba(30, 19, 50, 0.5);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text p {
  font-size: 1.1rem;
  color: var(--accent);
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.about-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  background: var(--glass);
  backdrop-filter: blur(20px);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.1), transparent);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--shadow-hover);
  border-color: var(--primary);
}

.stat-item:hover::before {
  left: 100%;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  display: block;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--muted);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); /* Responsive grid */
  gap: 3rem; /* Space between skill categories */
}

.skill-category h3 {
  font-size: 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text);
}

.skill-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skill-item {
  background: var(--glass);
  backdrop-filter: blur(20px);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.skill-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.08), transparent);
  transition: var(--transition);
}

.skill-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--shadow);
  border-color: var(--primary);
}

.skill-item:hover::before {
  left: 100%;
}

.skill-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.skill-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: var(--gradient);
  border-radius: 4px;
  transition: width 2s ease;
}

.skill-progress[data-progress="95"] { width: 95%; }
.skill-progress[data-progress="90"] { width: 90%; }
.skill-progress[data-progress="85"] { width: 85%; }
.skill-progress[data-progress="80"] { width: 80%; }
.skill-progress[data-progress="75"] { width: 75%; }

.achievements-section {
  background: rgba(30, 19, 50, 0.5); /* Darker background for contrast */
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.achievement-card {
  background: var(--glass);
  backdrop-filter: blur(20px);
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.achievement-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.1), transparent);
  transition: var(--transition);
}

.achievement-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: var(--shadow-hover);
  border-color: var(--primary);
}

.achievement-card:hover::before {
  left: 100%;
}

.achievement-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.achievement-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text);
}

.achievement-card p {
  color: var(--accent);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.achievement-year {
  background: var(--gradient);
  color: var(--text);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.downloads-section {
  margin-top: 3rem; /* Space from previous section */
}

.downloads-section h3 {
  color: var(--text);
  margin-bottom: 2rem;
  font-size: 1.8rem;
  text-align: center;
}

.downloads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.download-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.download-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 0 25px rgba(6, 182, 212, 0.3);
  border-color: var(--primary);
}

.download-icon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  display: block;
}

.download-card h4 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.download-card p {
  color: var(--muted);
  margin-bottom: 2rem;
  font-size: 1rem;
}

.download-btn {
  background: var(--gradient);
  color: var(--text);
  border: none;
  padding: 0.9rem 1.8rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
  opacity: 0.6;
}

.download-btn:not(:disabled):hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

.download-btn:not(:disabled):active {
  transform: translateY(0) scale(0.98);
}

.download-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-icon {
  font-size: 1.1rem;
}

.download-status {
  margin-top: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.download-status.success {
  color: #10b981;
}

.download-status.error {
  color: #ef4444;
}

.upload-section {
  margin-top: 3rem; /* Space from downloads section */
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05); /* Subtle background */
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.upload-section h3 {
  color: var(--text);
  margin-bottom: 2rem;
  font-size: 1.8rem;
  text-align: center;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.upload-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.upload-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 0 20px rgba(6, 182, 212, 0.2);
  border-color: var(--primary);
}

.upload-area {
  text-align: center;
  padding: 2rem;
  border: 2px dashed rgba(6, 182, 212, 0.3);
  border-radius: 10px;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.upload-area:hover {
  border-color: var(--primary);
  background: rgba(6, 182, 212, 0.05);
}

.upload-area.dragover {
  border-color: var(--primary-light);
  background: rgba(6, 182, 212, 0.1);
  transform: scale(1.02);
}

.upload-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.upload-area h4 {
  color: var(--text);
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.upload-area p {
  color: var(--muted);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.upload-status {
  margin-top: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.upload-status.success {
  color: #10b981;
}

.upload-status.error {
  color: #ef4444;
}

.uploaded-files {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.uploaded-files h4 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.files-list {
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem;
  background: rgba(6, 182, 212, 0.1);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  border: 1px solid rgba(6, 182, 212, 0.2);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.file-icon {
  font-size: 1.5rem;
}

.file-details h5 {
  color: var(--text);
  margin: 0;
  font-size: 0.9rem;
}

.file-details p {
  color: var(--muted);
  margin: 0;
  font-size: 0.8rem;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

.file-btn {
  background: none;
  border: 1px solid var(--primary);
  color: var(--primary);
  padding: 0.3rem 0.8rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: var(--transition);
}

.file-btn:hover {
  background: var(--primary);
  color: var(--text);
}

.file-btn.delete {
  border-color: #ef4444;
  color: #ef4444;
}

.file-btn.delete:hover {
  background: #ef4444;
  color: white;
}

.no-files {
  color: var(--muted);
  text-align: center;
  padding: 2rem;
  font-style: italic;
}

.project-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* Responsive project cards */
  gap: 2rem; /* Space between project cards */
}

.project-card {
  background: var(--glass);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  position: relative;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.08), transparent);
  transition: var(--transition);
  z-index: 1;
}

.project-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-hover);
  border-color: var(--primary);
}

.project-card:hover::before {
  left: 100%;
}

.project-image {
  height: 200px;
  background: var(--gradient);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-placeholder {
  font-size: 4rem;
  opacity: 0.8;
}

.project-content {
  padding: 1.5rem;
}

.project-content h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text);
}

.project-content p {
  color: var(--accent);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tech-tag {
  background: rgba(6, 182, 212, 0.2);
  color: var(--text);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  flex: 1;
  text-align: center;
  color: var(--text);
  text-decoration: none;
  padding: 0.8rem;
  border: 2px solid var(--primary);
  border-radius: 8px;
  transition: var(--transition);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.project-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient);
  transform: translateY(100%);
  transition: var(--transition);
  z-index: -1;
}

.project-link:hover {
  border-color: var(--primary-light);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow);
  color: var(--text);
}

.project-link:hover::before {
  transform: translateY(0);
}

footer {
  background: rgba(30, 41, 59, 0.8); /* Semi-transparent dark background */
  padding: 3rem 0 2rem; /* Generous padding */
  text-align: center;
  border-top: 1px solid rgba(6, 182, 212, 0.2); /* Subtle top border */
}

footer p {
  color: var(--muted);
  margin-bottom: 0.5rem;
}

footer p:last-child {
  margin-bottom: 0;
  font-size: 0.9rem;
  opacity: 0.7;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandWidth {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

@keyframes morphBlob {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translateY(0) rotate(0deg);
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: translateY(-10px) rotate(90deg);
  }
  50% {
    border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
    transform: translateY(0) rotate(180deg);
  }
  75% {
    border-radius: 60% 40% 60% 30% / 60% 30% 60% 40%;
    transform: translateY(10px) rotate(270deg);
  }
}

/* Modal animations */
@keyframes fadeInModal {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOutModal {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  position: relative;
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: var(--text) !important;
}

.download-file-btn:hover {
  background: var(--primary-dark, #0891b2) !important;
  transform: translateY(-1px);
}

.download-all-btn:hover {
  background: var(--primary-dark, #0891b2) !important;
  transform: translateY(-1px);
}

.cancel-btn:hover {
  background: var(--muted, #888) !important;
  color: var(--text) !important;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem; /* Reduced padding for smaller screens */
  }

  .nav-content {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
  }

  .hero .container {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .hero {
    padding: 4rem 0;
    min-height: auto;
  }

  .hero-stats {
    justify-content: center;
  }

  .hero-image::before {
    width: 350px;
    height: 350px;
  }

  .hero-image img {
    max-width: 280px;
    height: 320px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .downloads-grid {
    grid-template-columns: 1fr;
  }

  .download-card {
    padding: 2rem 1.5rem;
  }

  .upload-grid {
    grid-template-columns: 1fr;
  }

  .project-cards {
    grid-template-columns: 1fr;
  }

  section {
    padding: 4rem 0;
  }
}

/* Small Mobile Styles (480px and below) */
@media (max-width: 480px) {
  .hero-stats {
    flex-direction: column; /* Stack stats vertically */
    gap: 1rem;
  }

  .about-stats {
    flex-direction: column;
  }

  .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .hero-image::before {
    width: 300px;
    height: 300px;
  }

  .hero-image img {
    max-width: 250px;
    height: 280px;
  }

  section {
    padding: 3rem 0;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }
}

@media (prefers-reduced-motion: reduce) {
  /* Remove all animations and transitions globally */
  *,
  *::before,
  *::after {
    animation: none !important;
    transition: none !important;
  }

  .nav-link:hover,
  .nav-link.active,
  .logo:hover,
  .hero-image img:hover,
  .cta-btn:hover,
  .stat:hover,
  .stat-item:hover,
  .skill-item:hover,
  .achievement-card:hover,
  .project-card:hover,
  .project-link:hover,
  section h2:hover,
  .subtitle:hover {
    transform: none !important;
  }

  .nav-link:hover,
  .nav-link.active,
  .navbar:hover {
    box-shadow: none !important;
  }

  .logo:hover {
    text-shadow: none !important;
  }

  .hero-image::before,
  .shape,
  .hero-content,
  .hero-image,
  .main-title,
  .subtitle,
  .hero-description,
  .hero-stats,
  .cta-btn,
  .nav-menu li,
  .navbar {
    animation: none !important;
  }

  /* Reset section opacity */
  section {
    opacity: 1 !important;
  }
}
