# Portfolio Files Directory

This directory contains the public files that users can download from your portfolio website.

## 🚀 PRODUCTION SETUP INSTRUCTIONS:

### **Step 1: Add Your Files**
Place your actual files in this directory with these exact names:
- `Adit_Resume.pdf` - Your CV/Resume
- `Web_Development_Certificate.pdf` - Your web development certificate
- `UI_Design_Award.pdf` - Your UI design award certificate
- `Project_Documentation.pdf` - Your project documentation

### **Step 2: File Requirements**
- **Supported formats**: PDF, DOC, DOCX for CV; PDF, JPG, PNG for certificates; PDF, ZIP for projects
- **Maximum file size**: 10MB per file
- **Exact filenames**: Must match the names specified above

### **Step 3: Upload to Your Domain**
- Upload the entire website folder to your hosting server
- Ensure this `assets/files/` directory is accessible via web browser
- Test by visiting: `https://yourdomain.com/assets/files/Adit_Resume.pdf`

## 🔒 Security Features:

- **Admin mode is DISABLED** on hosted domains (only works on localhost)
- **Only real files** that exist in this directory will be available for download
- **No placeholder files** - if a file doesn't exist, the download button won't be enabled

## 📁 Current File Status:

The website will automatically detect which files exist and enable download buttons accordingly:

- **CV/Resume**: `Adit_Resume.pdf` ❌ (Add your file)
- **Certificates**:
  - `Web_Development_Certificate.pdf` ❌ (Add your file)
  - `UI_Design_Award.pdf` ❌ (Add your file)
- **Project Files**:
  - `Project_Documentation.pdf` ❌ (Add your file)

## 🎯 How It Works:

1. **Development** (localhost): Admin mode available for testing
2. **Production** (your domain): Admin mode completely disabled, only real files downloadable
3. **File Detection**: Website checks if files exist before showing download options
4. **User Experience**: Visitors can only download files you've actually uploaded

## ⚠️ Important Notes:

- Delete this README.md file before hosting (optional)
- Test all download links after uploading to your domain
- Files must be accessible via direct URL for downloads to work
