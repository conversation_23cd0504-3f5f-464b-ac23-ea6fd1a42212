# Portfolio Files Directory

This directory contains the public files that users can download from your portfolio website.

## Instructions:

1. **Replace the placeholder files** with your actual documents:
   - `Adit_Resume.pdf` - Your actual CV/Resume
   - `Web_Development_Certificate.pdf` - Your web development certificate
   - `UI_Design_Award.pdf` - Your UI design award certificate
   - `Project_Documentation.pdf` - Your project documentation

2. **File Requirements:**
   - Keep the same filenames as specified in the JavaScript code
   - Supported formats: PDF, DOC, DOCX for CV; PDF, JPG, PNG for certificates; PDF, ZIP for projects
   - Maximum file size: 10MB per file

3. **To add more files:**
   - Add the file to this directory
   - Update the `publicFiles` array in `js/script.js` with the new file information

## Current Public Files:

- **CV/Resume**: `Adit_Resume.pdf`
- **Certificates**: 
  - `Web_Development_Certificate.pdf`
  - `UI_Design_Award.pdf`
- **Project Files**: 
  - `Project_Documentation.pdf`

## Note:
These files will be available for download by all website visitors. Admin-uploaded files (via the secret admin mode) will override these public files for that browser session.
