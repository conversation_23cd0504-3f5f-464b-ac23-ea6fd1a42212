<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Adit - Portfolio</title>
  <link rel="stylesheet" href="css/style.css" />
  <script defer src="js/script.js"></script>
</head>
<body>
  <!-- Background shapes -->
  <div class="bg-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>

  <nav class="navbar">
    <div class="container">
      <div class="nav-content">
        <div class="logo">Adit</div>
        <ul class="nav-menu">
          <li><a href="#home" class="nav-link active">home</a></li>
          <li><a href="#about" class="nav-link">about</a></li>
          <li><a href="#skills" class="nav-link">skills</a></li>
          <li><a href="#achievements" class="nav-link">achievements</a></li>
          <li><a href="#projects" class="nav-link">projects</a></li>
        </ul>
      </div>
    </div>
  </nav>

  <header class="hero" id="home">
    <div class="container">
      <div class="hero-content">
        <h1 class="main-title">Hi, I'm Adit</h1>
        <p class="subtitle">Full-Stack Developer & Creative Problem Solver</p>
        <p class="hero-description">
          I'm a passionate web developer with 1.5+ years of experience creating innovative digital solutions.
          I specialize in modern JavaScript frameworks, responsive design, and user-centered development.
          My goal is to bridge the gap between idea, design and functionality, delivering exceptional web experiences
          that not only look great but perform flawlessly across all devices.
        </p>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">5+</span>
            <span class="stat-text">Projects</span>
          </div>
          <div class="stat">
            <span class="stat-number">1.5+</span>
            <span class="stat-text">Years</span>
          </div>
          <div class="stat">
            <span class="stat-number">100%</span>
            <span class="stat-text">Satisfaction</span>
          </div>
        </div>
        <a href="#projects" class="cta-btn">View My Work</a>
      </div>
      <div class="hero-image">
        <img src="assets/images/adit.jpg" alt="Adit - Full-Stack Developer" />
      </div>
    </div>
  </header>

  <section id="about" class="about-section fade-in">
    <div class="container">
      <h2>About Me</h2>
      <div class="about-content">
        <div class="about-text">
          <p>
            I'm Adit, a passionate web developer with a keen eye for modern UI design and frontend development.
            I specialize in creating beautiful, functional digital experiences using cutting-edge web technologies.
          </p>
          <p>
            My journey in web development started with a fascination for how design and code can come together to
            create engaging user interfaces. I love exploring new technologies and pushing the boundaries of what's
            possible on the web.
          </p>
          <p>
            When I'm not coding, you'll find me staying up-to-date with the latest design trends, contributing to
            open-source projects, or experimenting with new frameworks and tools.
          </p>
        </div>
        <div class="about-stats">
          <div class="stat-item">
            <span class="stat-number">5+</span>
            <span class="stat-label">Projects Completed</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">1.5+</span>
            <span class="stat-label">Years Experience</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">100%</span>
            <span class="stat-label">Client Satisfaction</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="skills" class="skills-section fade-in">
    <div class="container">
      <h2>🚀 Technical Skills</h2>
      <div class="skills-grid">
        <div class="skill-category">
          <h3>Frontend Development</h3>
          <div class="skill-items">
            <div class="skill-item">
              <span class="skill-name" data-progress="95">HTML5</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="95"></div>
              </div>
            </div>
            <div class="skill-item">
              <span class="skill-name" data-progress="90">CSS3 / Sass</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="90"></div>
              </div>
            </div>
            <div class="skill-item">
              <span class="skill-name" data-progress="85">JavaScript</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="85"></div>
              </div>
            </div>
            <div class="skill-item">
              <span class="skill-name" data-progress="80">React</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="80"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="skill-category">
          <h3>Design & Tools</h3>
          <div class="skill-items">
            <div class="skill-item">
              <span class="skill-name">UI/UX Design</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="85"></div>
              </div>
            </div>
            <div class="skill-item">
              <span class="skill-name">Figma</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="80"></div>
              </div>
            </div>
            <div class="skill-item">
              <span class="skill-name">Git/GitHub</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="75"></div>
              </div>
            </div>
            <div class="skill-item">
              <span class="skill-name">Responsive Design</span>
              <div class="skill-bar">
                <div class="skill-progress" data-progress="90"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="achievements" class="achievements-section fade-in">
    <div class="container">
      <h2>🏆 Achievements</h2>
      <div class="achievements-grid">
        <div class="achievement-card">
          <div class="achievement-icon">🎓</div>
          <h3>Web Development Certification</h3>
          <p>Completed comprehensive web development bootcamp with focus on modern JavaScript frameworks</p>
          <span class="achievement-year">2024</span>
        </div>

        <div class="achievement-card">
          <div class="achievement-icon">🏅</div>
          <h3>Best UI Design Award</h3>
          <p>Won first place in university's annual web design competition for innovative user interface</p>
          <span class="achievement-year">2023</span>
        </div>

        <div class="achievement-card">
          <div class="achievement-icon">🌟</div>
          <h3>Open Source Contributor</h3>
          <p>Active contributor to popular open-source projects with 100+ commits and community recognition</p>
          <span class="achievement-year">2023-2024</span>
        </div>
      </div>

      <!-- Downloads Section (Public) -->
      <div class="downloads-section">
        <h3>📁 Downloads</h3>
        <div class="downloads-grid">
          <!-- CV Download -->
          <div class="download-card">
            <div class="download-icon">📄</div>
            <h4>Download My CV</h4>
            <p>Get my latest resume in PDF format</p>
            <button class="download-btn" id="download-cv-btn" disabled>
              <span class="btn-icon">⬇️</span>
              Download CV
            </button>
            <div class="download-status" id="cv-download-status"></div>
          </div>

          <!-- Certificates Download -->
          <div class="download-card">
            <div class="download-icon">🏆</div>
            <h4>Download Certificates</h4>
            <p>View my achievement certificates</p>
            <button class="download-btn" id="download-cert-btn" disabled>
              <span class="btn-icon">⬇️</span>
              Download Certificates
            </button>
            <div class="download-status" id="cert-download-status"></div>
          </div>

          <!-- Project Files Download -->
          <div class="download-card">
            <div class="download-icon">💼</div>
            <h4>Download Project Files</h4>
            <p>Access my project documentation</p>
            <button class="download-btn" id="download-project-btn" disabled>
              <span class="btn-icon">⬇️</span>
              Download Projects
            </button>
            <div class="download-status" id="project-download-status"></div>
          </div>
        </div>
      </div>

      <!-- Admin Upload Section (Hidden by default) -->
      <div class="upload-section admin-only" style="display: none;">
        <h3>📄 Upload Documents (Admin Only)</h3>
        <div class="upload-grid">
          <div class="upload-card">
            <div class="upload-area" id="cv-upload">
              <div class="upload-icon">📄</div>
              <h4>Upload CV/Resume</h4>
              <p>Drag & drop your CV or click to browse</p>
              <input type="file" id="cv-file" accept=".pdf,.doc,.docx" hidden>
              <div class="upload-status" id="cv-status"></div>
            </div>
          </div>

          <div class="upload-card">
            <div class="upload-area" id="cert-upload">
              <div class="upload-icon">🏆</div>
              <h4>Upload Certificates</h4>
              <p>Drag & drop certificates or click to browse</p>
              <input type="file" id="cert-file" accept=".pdf,.jpg,.jpeg,.png" multiple hidden>
              <div class="upload-status" id="cert-status"></div>
            </div>
          </div>

          <div class="upload-card">
            <div class="upload-area" id="project-upload">
              <div class="upload-icon">💼</div>
              <h4>Upload Project Files</h4>
              <p>Drag & drop project documents or click to browse</p>
              <input type="file" id="project-file" accept=".pdf,.zip,.rar" multiple hidden>
              <div class="upload-status" id="project-status"></div>
            </div>
          </div>
        </div>

        <div class="uploaded-files">
          <h4>📁 Uploaded Files</h4>
          <div class="files-list" id="files-list">
            <p class="no-files">No files uploaded yet</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="projects" class="projects-section fade-in">
    <div class="container">
      <h2>💼 Featured Projects</h2>
      <div class="project-cards">
        <div class="project-card">
          <div class="project-image">
            <div class="project-placeholder">🌐</div>
          </div>
          <div class="project-content">
            <h3>E-Commerce Platform</h3>
            <p>Full-stack e-commerce solution with React frontend, Node.js backend, prisma for the database</p>
            <div class="project-tech">
              <span class="tech-tag">React</span>
              <span class="tech-tag">Node.js</span>
              <span class="tech-tag">Prisma</span>
            </div>
            <div class="project-links">
              <a href="#" class="project-link">Live Demo</a>
              <a href="#" class="project-link">GitHub</a>
            </div>
          </div>
        </div>

        <!-- <div class="project-card">
          <div class="project-image">
            <div class="project-placeholder">📱</div>
          </div>
          <div class="project-content">
            <h3>Task Management App</h3>
            <p>Responsive web application for task management with drag-and-drop functionality and real-time updates.</p>
            <div class="project-tech">
              <span class="tech-tag">JavaScript</span>
              <span class="tech-tag">CSS3</span>
              <span class="tech-tag">LocalStorage</span>
            </div>
            <div class="project-links">
              <a href="#" class="project-link">Live Demo</a>
              <a href="#" class="project-link">GitHub</a>
            </div>
          </div>
        </div> -->

        <div class="project-card">
          <div class="project-image">
            <div class="project-placeholder">🎨</div>
          </div>
          <div class="project-content">
            <h3>Portfolio Website</h3>
            <p>Modern, responsive portfolio website with smooth animations, dark mode, and optimized performance.</p>
            <div class="project-tech">
              <span class="tech-tag">HTML5</span>
              <span class="tech-tag">CSS3</span>
              <span class="tech-tag">JavaScript</span>
            </div>
            <div class="project-links">
              <a href="#" class="project-link">Live Demo</a>
              <a href="#" class="project-link">GitHub</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <footer>
    <div class="container">
      <p>© 2025 Adit. All rights reserved.</p>
    </div>
  </footer>
</body>
</html>
